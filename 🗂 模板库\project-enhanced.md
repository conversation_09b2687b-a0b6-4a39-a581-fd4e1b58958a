---
项目名称: <% tp.file.title %>
创建日期: <% tp.date.now("YYYY-MM-DD") %>
预计完成日期: <% tp.date.now("YYYY-MM-DD", "P30D") %>
状态: 待办
优先级: 中
tags:
  - 项目
  - 学习
  - <% await tp.system.prompt("项目类型（如：数学、英语、编程等）") %>
cssclasses:
  - project-page
status: Todo
---
# 📌 项目：<% tp.file.title %>

> **项目概述：** <% await tp.system.prompt("简要描述项目目标和意义") %>

## 🎯 项目目标

### 主要目标
- <% await tp.system.prompt("主要学习目标1") %>
- <% await tp.system.prompt("主要学习目标2（可选）", "") %>

### 成功标准
- [ ] 完成所有核心学习内容
- [ ] 通过相关测试或考核
- [ ] 能够实际应用所学知识

## 📚 学习资源

### 主要教材
- 

### 参考资料
- 

### 在线资源
- 

## 📅 项目时间规划

| 阶段 | 任务描述 | 开始日期 | 截止日期 | 状态 | 备注 |
|------|----------|----------|----------|------|------|
| 阶段一 | 基础知识学习 | <% tp.date.now("YYYY-MM-DD") %> | <% tp.date.now("YYYY-MM-DD", "P10D") %> | ⏳ 计划中 | |
| 阶段二 | 深入学习与练习 | <% tp.date.now("YYYY-MM-DD", "P10D") %> | <% tp.date.now("YYYY-MM-DD", "P20D") %> | ⏳ 计划中 | |
| 阶段三 | 总结与应用 | <% tp.date.now("YYYY-MM-DD", "P20D") %> | <% tp.date.now("YYYY-MM-DD", "P30D") %> | ⏳ 计划中 | |

## ✅ 任务清单

### 🔥 高优先级任务
- [ ] #task/<% tp.file.title %> 制定详细学习计划 📅 <% tp.date.now("YYYY-MM-DD", "P3D") %> ⏫ #priority/high
- [ ] #task/<% tp.file.title %> 收集学习资料 📅 <% tp.date.now("YYYY-MM-DD", "P5D") %> ⏫ #priority/high

### 📌 常规任务
- [ ] #task/<% tp.file.title %> 每日学习记录 🔄 every day #priority/medium
- [ ] #task/<% tp.file.title %> 周进度回顾 🔄 every week #priority/medium

### 🔽 低优先级任务
- [ ] #task/<% tp.file.title %> 整理学习笔记 📅 <% tp.date.now("YYYY-MM-DD", "P25D") %> #priority/low

## 📊 进度跟踪

```dataviewjs
// 自动计算项目完成度
const currentFile = dv.current();
const allTasks = currentFile.file.tasks;
const completedTasks = allTasks.where(t => t.completed);
const totalTasks = allTasks.length;

if (totalTasks > 0) {
  const completionRate = Math.round((completedTasks.length / totalTasks) * 100);
  const progressBar = "█".repeat(Math.floor(completionRate / 5)) + 
                     "░".repeat(20 - Math.floor(completionRate / 5));
  
  dv.paragraph(`**项目完成度：** ${completionRate}% (${completedTasks.length}/${totalTasks})`);
  dv.paragraph(`**进度条：** ${progressBar}`);
  
  // 预计完成时间
  if (completionRate > 0 && completionRate < 100) {
    const daysElapsed = Math.floor((new Date() - new Date(currentFile.创建日期)) / (1000 * 60 * 60 * 24));
    const estimatedTotalDays = Math.round(daysElapsed / (completionRate / 100));
    const remainingDays = estimatedTotalDays - daysElapsed;
    
    dv.paragraph(`**预计剩余时间：** ${remainingDays > 0 ? remainingDays : 0} 天`);
  }
} else {
  dv.paragraph("📝 暂无任务数据");
}
```

## 📈 项目日志

### <% tp.date.now("YYYY-MM-DD") %> - 项目启动
- 项目创建
- 初步规划完成

## 🤔 学习反思

### 遇到的挑战
- 

### 解决方案
- 

### 经验教训
- 

## 📝 项目总结

> 项目完成后填写

### 主要成果
- 

### 技能提升
- 

### 改进建议
- 

---
*最后更新：<% tp.date.now("YYYY-MM-DD HH:mm") %>*
