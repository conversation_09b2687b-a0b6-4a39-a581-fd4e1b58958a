module.exports = async (params) => {
    const { quickAddApi: { inputPrompt }, app } = params;
    
    // 获取当前时间
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm 格式
    
    // 获取今天的日记文件路径
    const today = now.toISOString().slice(0, 10); // YYYY-MM-DD 格式
    const dailyNotePath = `🎓 study/📆 学习日志/日记/${today}.md`;
    
    try {
        // 检查今天的日记是否存在
        const dailyFile = app.vault.getAbstractFileByPath(dailyNotePath);
        if (!dailyFile) {
            new Notice(`今天的日记文件不存在: ${dailyNotePath}`);
            return;
        }
        
        // 提示用户输入学习内容（一行输入）
        const input = await inputPrompt("快速记录 (格式: 开始时间-结束时间 学习内容):");
        if (!input) {
            new Notice("输入不能为空");
            return;
        }
        
        // 解析输入
        let timeRange = "";
        let activity = "";
        let duration = "";
        
        // 尝试解析时间格式 "HH:mm-HH:mm 内容" 或 "HH:mm- 内容"
        const timePattern = /^(\d{2}:\d{2})-(\d{2}:\d{2})?\s+(.+)$/;
        const match = input.match(timePattern);
        
        if (match) {
            const startTime = match[1];
            const endTime = match[2] || currentTime;
            activity = match[3];
            
            // 计算时长
            const [startHour, startMin] = startTime.split(':').map(Number);
            const [endHour, endMin] = endTime.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            let diffMinutes = endMinutes - startMinutes;
            
            if (diffMinutes < 0) {
                diffMinutes += 24 * 60;
            }
            
            timeRange = `${startTime} - ${endTime}`;
            duration = `${diffMinutes}min`;
        } else {
            // 如果格式不匹配，使用简单格式
            timeRange = `<开始时间> - ${currentTime}`;
            activity = input;
            duration = "?min";
        }
        
        // 生成简化的事件日志条目
        const eventLogEntry = `
### 📌 ${currentTime} 事件日志
- **时间段：** \`${timeRange}\`  ${duration}
- **做了什么：** 
\t- ${activity}
- **当时想法与感悟：**
\t- 

`;
        
        // 读取当前日记内容
        const content = await app.vault.read(dailyFile);
        
        // 查找事件日志部分的位置
        const lines = content.split('\n');
        let insertIndex = -1;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('## 📌 事件日志')) {
                insertIndex = i + 1;
                break;
            }
        }
        
        if (insertIndex === -1) {
            new Notice("未找到事件日志部分，请检查日记模板");
            return;
        }
        
        // 在事件日志部分后插入新条目
        lines.splice(insertIndex, 0, eventLogEntry);
        const newContent = lines.join('\n');
        
        // 写入文件
        await app.vault.modify(dailyFile, newContent);
        
        new Notice(`快速事件日志已添加: ${activity.substring(0, 20)}...`);
        
    } catch (error) {
        console.error('添加快速事件日志时出错:', error);
        new Notice(`添加事件日志失败: ${error.message}`);
    }
};
