# QuickAdd 事件日志配置说明

## 📋 配置步骤

### 1. 安装 QuickAdd 插件
- 在 Obsidian 设置 → 社区插件中搜索并安装 "QuickAdd"
- 启用插件

### 2. 配置事件日志命令

#### 方法一：基础事件日志（推荐，最稳定）
1. 打开 QuickAdd 设置
2. 点击 "Add Choice" 添加新选择
3. 设置如下：
   - **Name**: `📌 添加事件日志`
   - **Type**: `Macro`
4. 点击 "Configure" 配置宏
5. 添加 "User Script" 步骤
6. 在脚本路径中输入：`🔧 配置文件（插件设置与脚本）/quickadd-basic-log.js`
7. 保存配置

#### 方法二：快速记录（一行输入）
1. 添加另一个选择：
   - **Name**: `⚡ 快速记录`
   - **Type**: `Macro`
2. 配置脚本路径：`🔧 配置文件（插件设置与脚本）/quickadd-simple-log.js`

### 3. 设置快捷键（可选）
1. 在 Obsidian 设置 → 快捷键中搜索 "QuickAdd"
2. 为 "📌 添加事件日志" 设置快捷键，例如 `Ctrl+Shift+L`
3. 为 "⚡ 快速记录" 设置快捷键，例如 `Ctrl+Shift+Q`

## 🚀 使用方法

### 基础事件日志模式（推荐）
1. 按快捷键或通过命令面板执行 "📌 添加事件日志"
2. 输入开始时间（可选，格式：HH:mm）
3. 自动插入到今天的日记中，内容部分留空供手动填写
4. 插入位置：事件日志部分的顶部（最新的在上面）

### 快速记录模式
1. 按快捷键或执行 "⚡ 快速记录"
2. 输入格式：`开始时间-结束时间 学习内容`
   - 例如：`14:30-15:20 数学习题练习`
   - 或者：`14:30- 数学习题练习`（自动使用当前时间作为结束时间）
3. 自动计算时长并插入日记

## 💡 使用技巧

1. **时间格式**：始终使用 24 小时制的 HH:mm 格式
2. **跨天处理**：脚本会自动处理跨天的时间计算
3. **文件检查**：如果今天的日记不存在，会提示创建
4. **智能插入位置**：
   - 如果光标在事件日志部分内，新条目会插入到光标附近的合适位置
   - 如果光标在某个事件日志条目内，会插入到该条目后面
   - 如果光标不在事件日志部分，会插入到事件日志部分的顶部
5. **自动定位**：添加后会自动打开今天的日记文件

## 🔧 自定义选项

如果需要修改脚本行为，可以编辑对应的 .js 文件：

- 修改时间格式
- 调整插入位置
- 自定义提示信息
- 添加更多字段

## 🎯 智能插入位置说明

### 插入逻辑
脚本会根据以下优先级确定插入位置：

1. **光标在事件日志条目内**：
   - 检测光标是否在某个 "### 📌 XX:XX 事件日志" 条目内
   - 如果是，新条目会插入到该条目的结束位置后面
   - 这样可以保持时间顺序的连续性

2. **光标在事件日志部分但不在具体条目内**：
   - 新条目会插入到光标当前行的合适位置
   - 避免破坏现有的条目结构

3. **光标不在事件日志部分**：
   - 新条目会插入到 "## 📌 事件日志" 标题的下方
   - 这是默认的插入位置

### 最佳使用方式
- **推荐做法**：将光标定位到你想要插入位置的事件日志条目内，然后执行 QuickAdd 命令
- **时间顺序**：新条目会自动按时间顺序插入，保持日志的连续性
- **快速操作**：无需手动滚动到特定位置，脚本会智能判断最佳插入点

## ❗ 注意事项

1. 确保今天的日记文件存在且包含 "## 📌 事件日志" 部分
2. 脚本路径必须正确指向 js 文件
3. 如果遇到权限问题，检查 Obsidian 的脚本执行权限设置
4. **重要**：使用智能插入功能时，请确保当前打开的文件是今天的日记文件
5. 如果光标位置检测失败，脚本会自动回退到默认插入位置
