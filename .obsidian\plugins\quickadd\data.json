{"choices": [{"id": "bbc69e06-f1dd-40fb-9a07-ea00ac4048c0", "name": "创建今日学习笔记", "type": "Template", "command": false, "templatePath": "🗂 模板库/daily.md", "fileNameFormat": {"enabled": true, "format": "{{DATE:YYYY-MM-DD}}"}, "folder": {"enabled": true, "folders": ["🎓 study/📆 学习日志/日记"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": true}, {"id": "fbf39aaa-464c-4648-b023-b76a8f19bc5c", "name": "📌 添加事件日志", "type": "Macro", "command": false, "macroId": "37c6e1d6-58a0-49d3-bc44-493735cb93f4"}, {"id": "7811bddb-6c5c-4a36-a951-1e2f49890f19", "name": "📌 添加项目任务到今日", "type": "Macro", "command": false, "macroId": "fe817f75-b962-4417-9ca6-d5ffaa2bfb4d"}, {"id": "8cd67ea7-4534-410f-b5a5-ec20e92f2f09", "name": "📊 浏览学习项目", "type": "Macro", "command": false, "macroId": "42da6860-1045-4376-8dfb-e32575fe3ad0"}], "macros": [{"name": "创建今日学习笔记", "id": "3912910a-fac8-4e8b-a480-423cac0a7841", "commands": [{"name": "quickadd-event-log", "type": "UserScript", "id": "b0cc0604-e565-4022-8425-91f331efcc51", "path": "🔧 配置文件（插件设置与脚本）/quickadd-event-log.js", "settings": {}}], "runOnStartup": false}, {"name": "📌 添加事件日志", "id": "37c6e1d6-58a0-49d3-bc44-493735cb93f4", "commands": [{"name": "quickadd-basic-log", "type": "UserScript", "id": "6753e4ac-c3bd-496c-89bf-421a3c3427cc", "path": "🔧 配置文件（插件设置与脚本）/quickadd-basic-log.js", "settings": {}}], "runOnStartup": true}, {"name": "📌 添加项目任务到今日", "id": "fe817f75-b962-4417-9ca6-d5ffaa2bfb4d", "commands": [{"name": "quickadd-project-task-selector", "type": "UserScript", "id": "8d2e3d2c-0c4d-418b-836f-4b82bf37dcf8", "path": "🔧 配置文件（插件设置与脚本）/quickadd-project-task-selector.js", "settings": {}}], "runOnStartup": false}, {"name": "📊 浏览学习项目", "id": "42da6860-1045-4376-8dfb-e32575fe3ad0", "commands": [{"name": "quickadd-project-browser", "type": "UserScript", "id": "e52308c1-0710-4558-80bc-be829b01decd", "path": "🔧 配置文件（插件设置与脚本）/quickadd-project-browser.js", "settings": {}}], "runOnStartup": false}], "inputPrompt": "multi-line", "devMode": false, "templateFolderPath": "🗂 模板库", "announceUpdates": true, "version": "1.18.1", "disableOnlineFeatures": true, "enableRibbonIcon": true, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}