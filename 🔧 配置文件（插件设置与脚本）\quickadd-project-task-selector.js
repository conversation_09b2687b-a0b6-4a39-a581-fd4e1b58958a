module.exports = async (params) => {
    const { quickAddApi: { suggester, inputPrompt }, app } = params;
    
    try {
        // 获取今天的日记文件路径
        const today = new Date().toISOString().slice(0, 10);
        const dailyNotePath = `🎓 study/📆 学习日志/日记/${today}.md`;
        
        // 检查今天的日记是否存在
        const dailyFile = app.vault.getAbstractFileByPath(dailyNotePath);
        if (!dailyFile) {
            new Notice(`今天的日记文件不存在: ${dailyNotePath}`);
            return;
        }
        
        // 获取所有进行中的项目
        const projectFolder = app.vault.getAbstractFileByPath("🎓 study/📌 学习项目");
        if (!projectFolder) {
            new Notice("学习项目文件夹不存在");
            return;
        }
        
        const projectFiles = [];
        const getAllFiles = (folder) => {
            for (const child of folder.children) {
                if (child.extension === 'md' && child.name !== '项目管理看板.md') {
                    projectFiles.push(child);
                }
                if (child.children) {
                    getAllFiles(child);
                }
            }
        };
        
        getAllFiles(projectFolder);
        
        if (projectFiles.length === 0) {
            new Notice("没有找到项目文件");
            return;
        }
        
        // 解析项目文件，获取进行中的项目和任务
        const projectsWithTasks = [];
        
        for (const file of projectFiles) {
            const content = await app.vault.read(file);
            const lines = content.split('\n');
            
            // 检查项目状态
            let isActiveProject = false;
            let projectName = file.basename;
            
            for (const line of lines) {
                if (line.includes('状态') && (line.includes('进行中') || line.includes('待办'))) {
                    isActiveProject = true;
                    break;
                }
            }
            
            if (!isActiveProject) continue;
            
            // 提取未完成的任务
            const tasks = [];
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (line.match(/^- \[ \]/)) {
                    // 清理任务文本
                    let taskText = line.replace(/^- \[ \] /, '').replace(/#task\/?[^\s]*\s*/g, '').trim();
                    
                    // 检查优先级
                    let priority = '普通';
                    if (taskText.includes('⏫') || taskText.includes('🔥')) {
                        priority = '高优先级';
                    }
                    
                    tasks.push({
                        text: taskText,
                        priority: priority,
                        originalLine: line
                    });
                }
            }
            
            if (tasks.length > 0) {
                projectsWithTasks.push({
                    name: projectName,
                    file: file,
                    tasks: tasks
                });
            }
        }
        
        if (projectsWithTasks.length === 0) {
            new Notice("当前没有进行中的项目或未完成任务");
            return;
        }
        
        // 创建任务选择列表
        const taskOptions = [];
        for (const project of projectsWithTasks) {
            for (const task of project.tasks) {
                const priorityIcon = task.priority === '高优先级' ? '🔥 ' : '📌 ';
                taskOptions.push({
                    display: `${priorityIcon}[${project.name}] ${task.text}`,
                    project: project.name,
                    task: task.text,
                    priority: task.priority
                });
            }
        }
        
        // 按优先级排序
        taskOptions.sort((a, b) => {
            if (a.priority === '高优先级' && b.priority !== '高优先级') return -1;
            if (a.priority !== '高优先级' && b.priority === '高优先级') return 1;
            return 0;
        });
        
        // 让用户选择任务
        const selectedTask = await suggester(
            taskOptions.map(t => t.display),
            taskOptions,
            false,
            "选择要添加到今日学习任务的项目任务："
        );
        
        if (!selectedTask) {
            new Notice("未选择任务");
            return;
        }
        
        // 询问是否要修改任务描述
        const customDescription = await inputPrompt(
            "任务描述（留空使用原描述）:",
            selectedTask.task
        );
        
        const finalTaskText = customDescription || selectedTask.task;
        
        // 生成任务条目
        const priorityIcon = selectedTask.priority === '高优先级' ? '🔥' : '📌';
        const taskEntry = `- [ ] #task ${finalTaskText} ${priorityIcon}`;
        
        // 读取今日日记内容
        const dailyContent = await app.vault.read(dailyFile);
        const lines = dailyContent.split('\n');
        
        // 找到今日学习任务部分
        let insertIndex = -1;
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('今日学习任务') || lines[i].includes('### ✅ 今日学习任务')) {
                // 找到任务列表的插入位置
                for (let j = i + 1; j < lines.length; j++) {
                    if (lines[j].trim() === '' || lines[j].startsWith('###') || lines[j].startsWith('##')) {
                        insertIndex = j;
                        break;
                    }
                    if (lines[j].startsWith('- [ ]')) {
                        // 在现有任务后插入
                        continue;
                    }
                    if (!lines[j].startsWith('- [ ]') && lines[j].trim() !== '') {
                        insertIndex = j;
                        break;
                    }
                }
                if (insertIndex === -1) {
                    insertIndex = i + 1;
                }
                break;
            }
        }
        
        if (insertIndex === -1) {
            new Notice("未找到今日学习任务部分");
            return;
        }
        
        // 插入任务
        lines.splice(insertIndex, 0, taskEntry);
        const newContent = lines.join('\n');
        
        // 保存文件
        await app.vault.modify(dailyFile, newContent);
        
        new Notice(`已添加项目任务: ${finalTaskText.substring(0, 30)}...`);
        
        // 打开今天的日记文件
        const leaf = app.workspace.getLeaf(false);
        await leaf.openFile(dailyFile);
        
    } catch (error) {
        console.error('添加项目任务时出错:', error);
        new Notice(`添加项目任务失败: ${error.message}`);
    }
};
