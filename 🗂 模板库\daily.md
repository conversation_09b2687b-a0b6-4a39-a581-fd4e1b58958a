---
date: <% tp.date.now("YYYY-MM-DD") %>
area: 学习
tags:
  - 日程
aliases:
  - <% tp.date.now("YYYY年MM月DD日") %>
cssclasses:
  - daily-page
status: Todo
---
# 📅 今日学习计划：<% tp.date.now("YYYY-MM-DD dddd") %>

## ✅ 今日学习任务

### 🎯 手动添加的任务
- [ ] #task 高等数学习题练习 📅 <% tp.date.now("YYYY-MM-DD") %> ⏫ 🔄 every day
- [ ] #task 英语单词记忆 📅 <% tp.date.now("YYYY-MM-DD") %> 📌

### 📌 来自项目的任务

```dataview
TASK
FROM "🎓 study/📌 学习项目"
WHERE !completed AND contains(tags, "task")
SORT priority DESC, due ASC
```

### 🔥 高优先级项目任务

```dataviewjs
// 显示所有进行中项目的高优先级任务
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.status === "Todo" || p.状态 === "进行中");

let highPriorityTasks = [];

for (let page of projectPages) {
  const tasks = page.file.tasks.where(t => !t.completed && (
    t.text.includes("⏫") ||
    t.text.includes("🔥") ||
    t.text.includes("高优先级")
  ));

  for (let task of tasks) {
    highPriorityTasks.push([
      `[[${page.file.name}|${page.file.name}]]`,
      task.text,
      task.due || "无截止日期"
    ]);
  }
}

if (highPriorityTasks.length > 0) {
  dv.table(["项目", "任务", "截止日期"], highPriorityTasks);
} else {
  dv.paragraph("🎉 暂无高优先级项目任务");
}
```

## 📌 今日学习安排


- [ ] 08:00-09:00 📖 数学学习（理论） 
- [ ] 09:10-11:00 📝 数学练习（习题）
- [ ] 11:10-12:00 📚 英语学习（单词记忆）
- [ ] 12:00-13:30 🍽 午餐与休息
- [ ] 13:30-15:00 🖥️ 专业课复习
- [ ] 15:10-17:00 🧑🏻‍💻 知识巩固与扩展
- [ ] 17:10-18:00 📝 今日学习回顾
- [ ] 18:00-19:00 🍲 晚餐休息
- [ ] 19:00-21:00 📒 其他科目学习
- [ ] 21:00-21:30 📌 明日计划制定

## 📌 事件日志


## 📊 今日学习时长统计

```dataviewjs
// 自动计算当前文件的学习时长
const currentFile = dv.current();
const content = await dv.io.load(currentFile.file.path);
const lines = content.split('\n');

let totalMinutes = 0;
let sessionCount = 0;
let sessions = [];

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  if (line.includes('**时间段：**')) {
    const minuteMatch = line.match(/(\d+)min/);
    const timeMatch = line.match(/`([^`]+)`/);

    if (minuteMatch && timeMatch) {
      const minutes = parseInt(minuteMatch[1]);
      const timeRange = timeMatch[1];
      totalMinutes += minutes;
      sessionCount++;

      // 获取活动内容
      let activity = '';
      for (let j = i + 1; j < Math.min(i + 8, lines.length); j++) {
        if (lines[j].includes('**做了什么：**')) {
          for (let k = j + 1; k < lines.length; k++) {
            if (lines[k].includes('**当时想法与感悟：**')) break;
            if (lines[k].trim().startsWith('- ') || lines[k].trim().startsWith('\t- ')) {
              activity += lines[k].trim().replace(/^- /, '') + ' ';
            }
          }
          break;
        }
      }

      sessions.push([timeRange, `${minutes}分钟`, activity.trim()]);
    }
  }
}

const hours = Math.floor(totalMinutes / 60);
const remainingMinutes = totalMinutes % 60;
const totalTimeStr = hours > 0 ? `${hours}小时${remainingMinutes}分钟` : `${remainingMinutes}分钟`;

// 显示统计信息
dv.header(3, "📈 学习时长汇总");
dv.paragraph(`**总学习时长：** ${totalTimeStr} (${totalMinutes}分钟)`);
dv.paragraph(`**学习次数：** ${sessionCount}次`);
dv.paragraph(`**平均每次时长：** ${sessionCount > 0 ? Math.round(totalMinutes / sessionCount) : 0}分钟`);

if (sessions.length > 0) {
  dv.header(4, "📋 学习会话详情");
  dv.table(["时间段", "时长", "学习内容"], sessions);
}

// 学习效率分析
if (totalMinutes > 0) {
  dv.header(4, "📊 效率分析");
  const avgSessionLength = Math.round(totalMinutes / sessionCount);
  let efficiencyTip = "";

  if (avgSessionLength < 25) {
    efficiencyTip = "💡 建议：学习时间较短，可以尝试延长单次学习时间到25-50分钟";
  } else if (avgSessionLength > 90) {
    efficiencyTip = "💡 建议：单次学习时间较长，建议适当休息以保持专注力";
  } else {
    efficiencyTip = "✅ 很好：学习时间安排合理，保持这个节奏";
  }

  dv.paragraph(efficiencyTip);
}
```

## 📔 今日学习回顾

> 今日学习收获：

> 存在问题：

> 明日改进措施：