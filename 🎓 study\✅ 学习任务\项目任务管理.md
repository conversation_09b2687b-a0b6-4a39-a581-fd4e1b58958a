# 📌 项目任务管理中心

## 🎯 所有项目任务概览

```dataview
TABLE 
  file.link as 项目,
  length(file.tasks.where(t => !t.completed)) as 未完成任务数,
  length(file.tasks.where(t => t.completed)) as 已完成任务数,
  status as 项目状态
FROM "🎓 study/📌 学习项目"
WHERE file.name != "项目管理看板"
SORT status ASC, file.name ASC
```

## 🔥 高优先级任务

```dataview
TASK
FROM "🎓 study/📌 学习项目"
WHERE !completed AND (contains(text, "⏫") OR contains(text, "🔥"))
SORT file.name ASC
```

## 📅 有截止日期的任务

```dataview
TASK
FROM "🎓 study/📌 学习项目"
WHERE !completed AND due
SORT due ASC
```

## 📊 项目进度统计

```dataviewjs
// 计算每个项目的完成度
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.file.name !== "项目管理看板");

let projectStats = [];

for (let page of projectPages) {
  const allTasks = page.file.tasks;
  const completedTasks = allTasks.where(t => t.completed);
  const totalTasks = allTasks.length;
  
  if (totalTasks > 0) {
    const completionRate = Math.round((completedTasks.length / totalTasks) * 100);
    const progressBar = "█".repeat(Math.floor(completionRate / 10)) + 
                       "░".repeat(10 - Math.floor(completionRate / 10));
    
    projectStats.push([
      `[[${page.file.name}|${page.file.name}]]`,
      `${completedTasks.length}/${totalTasks}`,
      `${completionRate}%`,
      progressBar,
      page.status || "未设置"
    ]);
  }
}

if (projectStats.length > 0) {
  dv.table(
    ["项目名称", "完成情况", "完成率", "进度条", "状态"],
    projectStats
  );
} else {
  dv.paragraph("📝 暂无项目任务数据");
}
```

## 🚀 快速操作

### 添加项目任务到今日计划
使用 QuickAdd 命令：`📌 添加项目任务到今日`

### 创建新项目
使用模板：[[🗂 模板库/project.md]]

### 查看项目看板
[[🎓 study/📌 学习项目/项目管理看板]]

## 💡 使用建议

1. **优先级标记**：
   - ⏫ 高优先级（紧急重要）
   - 🔼 中优先级（重要不紧急）
   - 📌 普通优先级
   - 🔽 低优先级（不重要不紧急）

2. **截止日期格式**：使用 `📅 YYYY-MM-DD` 格式

3. **项目关联**：在每日任务中使用 `📂[[项目名称]]` 来关联项目

4. **定期回顾**：建议每周回顾项目进度，调整优先级和时间安排
