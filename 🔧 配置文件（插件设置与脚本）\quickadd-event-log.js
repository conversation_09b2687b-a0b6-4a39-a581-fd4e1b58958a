module.exports = async (params) => {
    const { quickAddApi: { inputPrompt, suggester }, app } = params;
    
    // 获取当前时间
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm 格式
    
    // 获取今天的日记文件路径
    const today = now.toISOString().slice(0, 10); // YYYY-MM-DD 格式
    const dailyNotePath = `🎓 study/📆 学习日志/日记/${today}.md`;
    
    try {
        // 检查今天的日记是否存在
        const dailyFile = app.vault.getAbstractFileByPath(dailyNotePath);
        if (!dailyFile) {
            new Notice(`今天的日记文件不存在: ${dailyNotePath}`);
            return;
        }
        
        // 提示用户输入开始时间
        const startTime = await inputPrompt("开始时间 (HH:mm 格式，留空则需要手动填写):");
        
        // 提示用户输入做了什么
        const activity = await inputPrompt("做了什么 (简要描述学习内容):");
        if (!activity) {
            new Notice("必须填写学习内容");
            return;
        }
        
        // 提示用户输入感悟
        const thoughts = await inputPrompt("当时想法与感悟 (可选):");
        
        // 计算时长（如果提供了开始时间）
        let timeRange = "";
        let duration = "";
        
        if (startTime && startTime.match(/^\d{2}:\d{2}$/)) {
            const [startHour, startMin] = startTime.split(':').map(Number);
            const [endHour, endMin] = currentTime.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            let diffMinutes = endMinutes - startMinutes;
            
            // 处理跨天情况
            if (diffMinutes < 0) {
                diffMinutes += 24 * 60;
            }
            
            timeRange = `${startTime} - ${currentTime}`;
            duration = `${diffMinutes}min`;
        } else {
            timeRange = `<开始时间> - ${currentTime}`;
            duration = "?min";
        }
        
        // 生成事件日志条目
        const eventLogEntry = `
### 📌 ${currentTime} 事件日志
- **时间段：** \`${timeRange}\`  ${duration}
- **做了什么：** 
\t- ${activity}
- **当时想法与感悟：**
\t- ${thoughts || ""}

`;
        
        // 读取当前日记内容
        const content = await app.vault.read(dailyFile);
        
        // 查找事件日志部分的位置
        const lines = content.split('\n');
        let insertIndex = -1;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('## 📌 事件日志')) {
                // 找到事件日志部分，在其后插入
                insertIndex = i + 1;
                break;
            }
        }
        
        if (insertIndex === -1) {
            new Notice("未找到事件日志部分，请检查日记模板");
            return;
        }
        
        // 在事件日志部分后插入新条目
        lines.splice(insertIndex, 0, eventLogEntry);
        const newContent = lines.join('\n');
        
        // 写入文件
        await app.vault.modify(dailyFile, newContent);
        
        new Notice(`事件日志已添加到 ${today}.md`);
        
        // 可选：打开今天的日记文件
        const leaf = app.workspace.getLeaf(false);
        await leaf.openFile(dailyFile);
        
    } catch (error) {
        console.error('添加事件日志时出错:', error);
        new Notice(`添加事件日志失败: ${error.message}`);
    }
};
