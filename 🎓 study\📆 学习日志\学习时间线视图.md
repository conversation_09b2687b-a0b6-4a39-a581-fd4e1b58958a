# 📈 学习时间线可视化

## 🕐 今日学习时间线

```dataviewjs
// 获取今天的日期
const today = new Date().toISOString().slice(0, 10);
const todayFile = dv.page(`🎓 study/📆 学习日志/日记/${today}`);

if (!todayFile) {
  dv.paragraph("📝 今天的日记文件不存在");
} else {
  // 读取今天的日记内容
  const content = await dv.io.load(todayFile.file.path);
  const lines = content.split('\n');
  
  let timelineData = [];
  
  // 解析事件日志
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.match(/^### 📌 \d{2}:\d{2} 事件日志$/)) {
      const timeMatch = line.match(/### 📌 (\d{2}:\d{2}) 事件日志/);
      const endTime = timeMatch ? timeMatch[1] : '';
      
      let startTime = '';
      let duration = '';
      let activity = '';
      
      // 查找时间段和活动信息
      for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
        const nextLine = lines[j];
        
        if (nextLine.includes('**时间段：**')) {
          const rangeMatch = nextLine.match(/`([^`]+)`\s*(\d+)min/);
          if (rangeMatch) {
            const timeRange = rangeMatch[1];
            duration = rangeMatch[2];
            const timeParts = timeRange.split(' - ');
            if (timeParts.length === 2) {
              startTime = timeParts[0];
            }
          }
        } else if (nextLine.includes('**做了什么：**')) {
          for (let k = j + 1; k < lines.length; k++) {
            if (lines[k].includes('**当时想法与感悟：**')) break;
            if (lines[k].trim().startsWith('- ') || lines[k].trim().startsWith('\t- ')) {
              activity += lines[k].trim().replace(/^- /, '') + ' ';
            }
          }
          break;
        }
      }
      
      if (startTime && endTime && activity) {
        timelineData.push({
          startTime,
          endTime,
          duration: parseInt(duration) || 0,
          activity: activity.trim()
        });
      }
    }
  }
  
  // 按开始时间排序
  timelineData.sort((a, b) => a.startTime.localeCompare(b.startTime));
  
  if (timelineData.length === 0) {
    dv.paragraph("📝 今天还没有学习记录");
  } else {
    // 创建时间线可视化
    dv.header(3, `📅 ${today} 学习时间线`);
    
    // 计算总学习时间
    const totalMinutes = timelineData.reduce((sum, item) => sum + item.duration, 0);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    dv.paragraph(`**总学习时长：** ${hours}小时${minutes}分钟`);
    dv.paragraph(`**学习次数：** ${timelineData.length}次`);
    
    // 生成时间线HTML
    let timelineHtml = '<div style="position: relative; margin: 20px 0;">';
    
    for (let i = 0; i < timelineData.length; i++) {
      const item = timelineData[i];
      const isEven = i % 2 === 0;
      
      timelineHtml += `
        <div style="
          display: flex; 
          align-items: center; 
          margin: 15px 0;
          ${isEven ? 'flex-direction: row;' : 'flex-direction: row-reverse;'}
        ">
          <div style="
            flex: 1; 
            padding: 10px 15px; 
            background: ${isEven ? '#f0f8ff' : '#fff8f0'}; 
            border-radius: 8px; 
            border-left: 4px solid ${isEven ? '#4a90e2' : '#f39c12'};
            margin: ${isEven ? '0 20px 0 0' : '0 0 0 20px'};
          ">
            <div style="font-weight: bold; color: #333;">
              ${item.startTime} - ${item.endTime} (${item.duration}分钟)
            </div>
            <div style="margin-top: 5px; color: #666;">
              ${item.activity}
            </div>
          </div>
          <div style="
            width: 12px; 
            height: 12px; 
            background: ${isEven ? '#4a90e2' : '#f39c12'}; 
            border-radius: 50%; 
            border: 3px solid white;
            box-shadow: 0 0 0 2px ${isEven ? '#4a90e2' : '#f39c12'};
          "></div>
        </div>
      `;
    }
    
    timelineHtml += '</div>';
    
    // 显示时间线
    const timelineContainer = dv.container.createDiv();
    timelineContainer.innerHTML = timelineHtml;
  }
}
```

## 📊 学习时间统计概览

```dataviewjs
// 彩虹色配色方案（浅色调）
const rainbowColors = [
  '#FFB3BA', // 浅红
  '#FFDFBA', // 浅橙
  '#FFFFBA', // 浅黄
  '#BAFFC9', // 浅绿
  '#BAE1FF', // 浅蓝
  '#D4BAFF', // 浅紫
  '#FFBAFF'  // 浅粉
];

// 获取学习数据的通用函数
async function getStudyData(days) {
  const today = new Date();
  let data = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().slice(0, 10);

    const dayFile = dv.page(`🎓 study/📆 学习日志/日记/${dateStr}`);

    if (dayFile) {
      const content = await dv.io.load(dayFile.file.path);
      const lines = content.split('\n');

      let dayTotal = 0;
      let sessionCount = 0;

      for (let line of lines) {
        if (line.includes('**时间段：**')) {
          const minuteMatch = line.match(/(\d+)min/);
          if (minuteMatch) {
            dayTotal += parseInt(minuteMatch[1]);
            sessionCount++;
          }
        }
      }

      data.push({
        date: dateStr,
        dayOfWeek: date.toLocaleDateString('zh-CN', { weekday: 'short' }),
        totalMinutes: dayTotal,
        sessions: sessionCount,
        month: date.getMonth() + 1,
        day: date.getDate()
      });
    }
  }

  return data.sort((a, b) => a.date.localeCompare(b.date));
}

// 创建美化的图表
function createChart(data, title, colorIndex, showDays = true) {
  if (data.length === 0) return;

  const maxMinutes = Math.max(...data.map(d => d.totalMinutes));
  const baseColor = rainbowColors[colorIndex % rainbowColors.length];
  const gradientColor = rainbowColors[(colorIndex + 1) % rainbowColors.length];

  let chartHtml = `
    <div style="
      margin: 25px 0;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      border: 1px solid rgba(255,255,255,0.8);
    ">
      <h4 style="
        margin: 0 0 15px 0;
        color: #495057;
        font-size: 16px;
        text-align: center;
        text-shadow: 0 1px 2px rgba(255,255,255,0.8);
      ">${title}</h4>
      <div style="
        display: flex;
        align-items: end;
        height: 180px;
        padding: 15px;
        background: rgba(255,255,255,0.7);
        border-radius: 10px;
        backdrop-filter: blur(10px);
      ">
  `;

  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    const height = maxMinutes > 0 ? (item.totalMinutes / maxMinutes) * 140 : 0;
    const hours = Math.floor(item.totalMinutes / 60);
    const minutes = item.totalMinutes % 60;

    // 为每个柱子分配不同的彩虹色
    const barColor = rainbowColors[i % rainbowColors.length];
    const barGradient = rainbowColors[(i + 2) % rainbowColors.length];

    chartHtml += `
      <div style="
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 2px;
        min-width: ${showDays ? '40px' : '15px'};
      ">
        <div style="
          width: ${showDays ? '28px' : '12px'};
          height: ${height}px;
          background: linear-gradient(to top, ${barColor}, ${barGradient});
          border-radius: 6px 6px 0 0;
          margin-bottom: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
          border: 1px solid rgba(255,255,255,0.6);
          transition: all 0.3s ease;
        " title="${item.totalMinutes}分钟, ${item.sessions}次学习">
        </div>
        <div style="font-size: ${showDays ? '11px' : '9px'}; text-align: center; color: #6c757d;">
          ${showDays ? `
            <div style="font-weight: bold; color: #495057;">${item.dayOfWeek}</div>
            <div style="color: #868e96;">${item.month}/${item.day}</div>
            <div style="color: ${barColor}; font-size: 9px; font-weight: bold;">
              ${hours > 0 ? hours + 'h' : ''}${minutes}m
            </div>
          ` : `
            <div style="color: ${barColor}; font-size: 8px; writing-mode: vertical-rl;">
              ${item.month}/${item.day}
            </div>
          `}
        </div>
      </div>
    `;
  }

  chartHtml += '</div></div>';

  const chartContainer = dv.container.createDiv();
  chartContainer.innerHTML = chartHtml;

  // 显示统计摘要
  const totalMinutes = data.reduce((sum, d) => sum + d.totalMinutes, 0);
  const totalSessions = data.reduce((sum, d) => sum + d.sessions, 0);
  const avgDaily = Math.round(totalMinutes / data.length);
  const totalHours = Math.floor(totalMinutes / 60);
  const remainingMinutes = totalMinutes % 60;

  const summaryHtml = `
    <div style="
      display: flex;
      justify-content: space-around;
      margin: 15px 0;
      padding: 15px;
      background: rgba(255,255,255,0.8);
      border-radius: 10px;
      backdrop-filter: blur(5px);
    ">
      <div style="text-align: center;">
        <div style="font-size: 18px; font-weight: bold; color: ${baseColor};">${totalHours}h${remainingMinutes}m</div>
        <div style="font-size: 12px; color: #6c757d;">总学习时长</div>
      </div>
      <div style="text-align: center;">
        <div style="font-size: 18px; font-weight: bold; color: ${gradientColor};">${totalSessions}</div>
        <div style="font-size: 12px; color: #6c757d;">总学习次数</div>
      </div>
      <div style="text-align: center;">
        <div style="font-size: 18px; font-weight: bold; color: ${rainbowColors[(colorIndex + 2) % rainbowColors.length]};">${Math.floor(avgDaily / 60)}h${avgDaily % 60}m</div>
        <div style="font-size: 12px; color: #6c757d;">日均时长</div>
      </div>
    </div>
  `;

  const summaryContainer = dv.container.createDiv();
  summaryContainer.innerHTML = summaryHtml;
}

// 获取并显示不同时间段的数据
const weekData = await getStudyData(7);
const monthData = await getStudyData(30);
const twoMonthData = await getStudyData(60);
const threeMonthData = await getStudyData(90);

createChart(weekData, "📈 最近一周学习时间线", 0);
createChart(monthData, "📊 最近一个月学习趋势", 1, false);
createChart(twoMonthData, "📉 最近两个月学习概览", 2, false);
createChart(threeMonthData, "📋 最近三个月学习总览", 3, false);

// 月度对比分析
dv.header(3, "📅 月度学习对比分析");

const monthlyStats = [];
const today = new Date();

for (let i = 0; i < 3; i++) {
  const monthStart = new Date(today.getFullYear(), today.getMonth() - i, 1);
  const monthEnd = new Date(today.getFullYear(), today.getMonth() - i + 1, 0);

  let monthTotal = 0;
  let monthSessions = 0;
  let activeDays = 0;

  for (let d = new Date(monthStart); d <= monthEnd; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().slice(0, 10);
    const dayFile = dv.page(`🎓 study/📆 学习日志/日记/${dateStr}`);

    if (dayFile) {
      const content = await dv.io.load(dayFile.file.path);
      const lines = content.split('\n');

      let dayMinutes = 0;
      let daySessions = 0;

      for (let line of lines) {
        if (line.includes('**时间段：**')) {
          const minuteMatch = line.match(/(\d+)min/);
          if (minuteMatch) {
            dayMinutes += parseInt(minuteMatch[1]);
            daySessions++;
          }
        }
      }

      if (dayMinutes > 0) {
        monthTotal += dayMinutes;
        monthSessions += daySessions;
        activeDays++;
      }
    }
  }

  monthlyStats.push({
    month: monthStart.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' }),
    totalMinutes: monthTotal,
    sessions: monthSessions,
    activeDays: activeDays,
    avgDaily: activeDays > 0 ? Math.round(monthTotal / activeDays) : 0
  });
}

// 创建月度对比图表
let monthlyHtml = `
  <div style="
    margin: 25px 0;
    padding: 20px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe0e6 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  ">
`;

for (let i = 0; i < monthlyStats.length; i++) {
  const stat = monthlyStats[i];
  const color = rainbowColors[i + 4];
  const hours = Math.floor(stat.totalMinutes / 60);
  const minutes = stat.totalMinutes % 60;

  monthlyHtml += `
    <div style="
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15px 0;
      padding: 15px;
      background: rgba(255,255,255,0.8);
      border-radius: 10px;
      border-left: 5px solid ${color};
    ">
      <div style="flex: 1;">
        <h4 style="margin: 0; color: #495057;">${stat.month}</h4>
      </div>
      <div style="display: flex; gap: 20px; text-align: center;">
        <div>
          <div style="font-size: 16px; font-weight: bold; color: ${color};">${hours}h${minutes}m</div>
          <div style="font-size: 11px; color: #6c757d;">总时长</div>
        </div>
        <div>
          <div style="font-size: 16px; font-weight: bold; color: ${color};">${stat.activeDays}</div>
          <div style="font-size: 11px; color: #6c757d;">活跃天数</div>
        </div>
        <div>
          <div style="font-size: 16px; font-weight: bold; color: ${color};">${Math.floor(stat.avgDaily / 60)}h${stat.avgDaily % 60}m</div>
          <div style="font-size: 11px; color: #6c757d;">日均时长</div>
        </div>
        <div>
          <div style="font-size: 16px; font-weight: bold; color: ${color};">${stat.sessions}</div>
          <div style="font-size: 11px; color: #6c757d;">学习次数</div>
        </div>
      </div>
    </div>
  `;
}

monthlyHtml += '</div>';

const monthlyContainer = dv.container.createDiv();
monthlyContainer.innerHTML = monthlyHtml;

// 学习习惯分析
dv.header(3, "🔍 学习习惯分析");

// 分析一周内每天的学习模式
const weekdayStats = {};
const hourStats = {};

for (let data of threeMonthData) {
  const date = new Date(data.date);
  const weekday = date.getDay(); // 0=周日, 1=周一, ...

  if (!weekdayStats[weekday]) {
    weekdayStats[weekday] = { totalMinutes: 0, days: 0 };
  }

  if (data.totalMinutes > 0) {
    weekdayStats[weekday].totalMinutes += data.totalMinutes;
    weekdayStats[weekday].days++;
  }
}

const weekdayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
let habitHtml = `
  <div style="
    margin: 25px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  ">
    <h4 style="margin: 0 0 20px 0; color: #495057; text-align: center;">📊 一周学习习惯分布</h4>
    <div style="display: flex; justify-content: space-around; align-items: end; height: 120px; padding: 10px;">
`;

for (let i = 0; i < 7; i++) {
  const stat = weekdayStats[i];
  const avgMinutes = stat ? Math.round(stat.totalMinutes / stat.days) : 0;
  const maxAvg = Math.max(...Object.values(weekdayStats).map(s => s ? Math.round(s.totalMinutes / s.days) : 0));
  const height = maxAvg > 0 ? (avgMinutes / maxAvg) * 80 : 0;
  const color = rainbowColors[i];

  habitHtml += `
    <div style="display: flex; flex-direction: column; align-items: center;">
      <div style="
        width: 25px;
        height: ${height}px;
        background: linear-gradient(to top, ${color}, ${rainbowColors[(i + 1) % rainbowColors.length]});
        border-radius: 4px 4px 0 0;
        margin-bottom: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      "></div>
      <div style="font-size: 11px; text-align: center; color: #6c757d;">
        <div style="font-weight: bold; color: ${color};">${weekdayNames[i]}</div>
        <div style="font-size: 9px;">${Math.floor(avgMinutes / 60)}h${avgMinutes % 60}m</div>
      </div>
    </div>
  `;
}

habitHtml += `
    </div>
    <div style="text-align: center; margin-top: 15px; font-size: 12px; color: #6c757d;">
      💡 基于最近三个月的数据分析平均每日学习时长
    </div>
  </div>
`;

const habitContainer = dv.container.createDiv();
habitContainer.innerHTML = habitHtml;

// 学习效率趋势分析
dv.header(3, "📈 学习效率趋势");

// 按周分组分析效率趋势
const weeklyTrends = [];
const weeksToAnalyze = 8; // 分析最近8周

for (let week = 0; week < weeksToAnalyze; week++) {
  const weekStart = new Date(today);
  weekStart.setDate(today.getDate() - (week * 7) - today.getDay());

  let weekTotal = 0;
  let weekSessions = 0;
  let weekDays = 0;

  for (let day = 0; day < 7; day++) {
    const date = new Date(weekStart);
    date.setDate(weekStart.getDate() + day);
    const dateStr = date.toISOString().slice(0, 10);

    const dayFile = dv.page(`🎓 study/📆 学习日志/日记/${dateStr}`);

    if (dayFile) {
      const content = await dv.io.load(dayFile.file.path);
      const lines = content.split('\n');

      let dayMinutes = 0;
      let daySessions = 0;

      for (let line of lines) {
        if (line.includes('**时间段：**')) {
          const minuteMatch = line.match(/(\d+)min/);
          if (minuteMatch) {
            dayMinutes += parseInt(minuteMatch[1]);
            daySessions++;
          }
        }
      }

      if (dayMinutes > 0) {
        weekTotal += dayMinutes;
        weekSessions += daySessions;
        weekDays++;
      }
    }
  }

  if (weekTotal > 0) {
    weeklyTrends.push({
      week: `第${weeksToAnalyze - week}周`,
      totalMinutes: weekTotal,
      sessions: weekSessions,
      activeDays: weekDays,
      avgSessionLength: weekSessions > 0 ? Math.round(weekTotal / weekSessions) : 0,
      efficiency: weekDays > 0 ? Math.round(weekTotal / weekDays) : 0
    });
  }
}

weeklyTrends.reverse(); // 按时间顺序排列

if (weeklyTrends.length > 0) {
  const maxEfficiency = Math.max(...weeklyTrends.map(w => w.efficiency));

  let trendHtml = `
    <div style="
      margin: 25px 0;
      padding: 20px;
      background: linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%);
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    ">
      <h4 style="margin: 0 0 20px 0; color: #495057; text-align: center;">📊 最近${weeksToAnalyze}周学习效率趋势</h4>
      <div style="display: flex; align-items: end; height: 140px; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 10px;">
  `;

  for (let i = 0; i < weeklyTrends.length; i++) {
    const trend = weeklyTrends[i];
    const height = maxEfficiency > 0 ? (trend.efficiency / maxEfficiency) * 100 : 0;
    const color = rainbowColors[i % rainbowColors.length];
    const nextColor = rainbowColors[(i + 1) % rainbowColors.length];

    trendHtml += `
      <div style="
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 3px;
      ">
        <div style="
          width: 20px;
          height: ${height}px;
          background: linear-gradient(to top, ${color}, ${nextColor});
          border-radius: 4px 4px 0 0;
          margin-bottom: 8px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          position: relative;
        " title="日均${Math.floor(trend.efficiency / 60)}h${trend.efficiency % 60}m">
          <div style="
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 8px;
            color: ${color};
            font-weight: bold;
            white-space: nowrap;
          ">${Math.floor(trend.efficiency / 60)}h${trend.efficiency % 60}m</div>
        </div>
        <div style="font-size: 9px; text-align: center; color: #6c757d;">
          <div style="font-weight: bold; color: ${color};">${trend.week}</div>
          <div style="font-size: 8px;">${trend.activeDays}天</div>
        </div>
      </div>
    `;
  }

  trendHtml += `
      </div>
      <div style="margin-top: 20px;">
        <div style="display: flex; justify-content: space-around; text-align: center;">
          <div>
            <div style="font-size: 14px; font-weight: bold; color: #28a745;">
              ${Math.floor(weeklyTrends[weeklyTrends.length - 1]?.efficiency / 60) || 0}h${(weeklyTrends[weeklyTrends.length - 1]?.efficiency % 60) || 0}m
            </div>
            <div style="font-size: 11px; color: #6c757d;">本周日均</div>
          </div>
          <div>
            <div style="font-size: 14px; font-weight: bold; color: #17a2b8;">
              ${Math.round(weeklyTrends.reduce((sum, w) => sum + w.avgSessionLength, 0) / weeklyTrends.length) || 0}min
            </div>
            <div style="font-size: 11px; color: #6c757d;">平均单次时长</div>
          </div>
          <div>
            <div style="font-size: 14px; font-weight: bold; color: #ffc107;">
              ${Math.round(weeklyTrends.reduce((sum, w) => sum + w.activeDays, 0) / weeklyTrends.length * 10) / 10 || 0}
            </div>
            <div style="font-size: 11px; color: #6c757d;">周均活跃天数</div>
          </div>
        </div>
      </div>
    </div>
  `;

  const trendContainer = dv.container.createDiv();
  trendContainer.innerHTML = trendHtml;
}

// 学习建议
dv.header(3, "💡 个性化学习建议");

const recentWeekData = weeklyTrends.slice(-2); // 最近两周数据
let suggestions = [];

if (recentWeekData.length >= 2) {
  const thisWeek = recentWeekData[1];
  const lastWeek = recentWeekData[0];

  // 效率对比
  if (thisWeek.efficiency > lastWeek.efficiency) {
    suggestions.push("🎉 学习效率有所提升，继续保持！");
  } else if (thisWeek.efficiency < lastWeek.efficiency) {
    suggestions.push("📈 学习效率有所下降，建议调整学习方法或休息安排");
  }

  // 活跃天数建议
  if (thisWeek.activeDays < 5) {
    suggestions.push("📅 建议增加学习频率，保持每周至少5天的学习习惯");
  }

  // 单次学习时长建议
  if (thisWeek.avgSessionLength < 25) {
    suggestions.push("⏰ 单次学习时间较短，建议延长到25-50分钟以提高专注度");
  } else if (thisWeek.avgSessionLength > 90) {
    suggestions.push("🛑 单次学习时间较长，建议适当休息以保持学习效率");
  }
}

// 基于一周习惯的建议
const bestDay = Object.keys(weekdayStats).reduce((a, b) =>
  (weekdayStats[a]?.totalMinutes || 0) > (weekdayStats[b]?.totalMinutes || 0) ? a : b
);
const worstDay = Object.keys(weekdayStats).reduce((a, b) =>
  (weekdayStats[a]?.totalMinutes || 0) < (weekdayStats[b]?.totalMinutes || 0) ? a : b
);

if (bestDay !== worstDay) {
  suggestions.push(`📊 ${weekdayNames[bestDay]}是你的高效学习日，${weekdayNames[worstDay]}可以适当增加学习时间`);
}

let suggestionHtml = `
  <div style="
    margin: 25px 0;
    padding: 20px;
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  ">
    <h4 style="margin: 0 0 15px 0; color: #495057; text-align: center;">🎯 基于数据的学习建议</h4>
`;

if (suggestions.length > 0) {
  for (let i = 0; i < suggestions.length; i++) {
    const color = rainbowColors[i % rainbowColors.length];
    suggestionHtml += `
      <div style="
        margin: 10px 0;
        padding: 12px;
        background: rgba(255,255,255,0.8);
        border-radius: 8px;
        border-left: 4px solid ${color};
        font-size: 14px;
        color: #495057;
      ">
        ${suggestions[i]}
      </div>
    `;
  }
} else {
  suggestionHtml += `
    <div style="
      text-align: center;
      padding: 20px;
      color: #6c757d;
      font-style: italic;
    ">
      继续记录学习数据，我们将为你提供个性化建议 📚
    </div>
  `;
}

suggestionHtml += '</div>';

const suggestionContainer = dv.container.createDiv();
suggestionContainer.innerHTML = suggestionHtml;
```

## 🎨 自定义样式

你可以通过 CSS 片段来进一步美化时间线显示效果。创建一个 CSS 文件并添加以下样式：

```css
/* 时间线样式优化 */
.timeline-container {
  position: relative;
  padding: 20px 0;
}

.timeline-item {
  position: relative;
  margin: 20px 0;
  padding-left: 30px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: -20px;
  width: 2px;
  background: #ddd;
}

.timeline-item::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4a90e2;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #4a90e2;
}
```
