# 📈 学习时间线可视化

## 🕐 今日学习时间线

```dataviewjs
// 获取今天的日期
const today = new Date().toISOString().slice(0, 10);
const todayFile = dv.page(`🎓 study/📆 学习日志/日记/${today}`);

if (!todayFile) {
  dv.paragraph("📝 今天的日记文件不存在");
} else {
  // 读取今天的日记内容
  const content = await dv.io.load(todayFile.file.path);
  const lines = content.split('\n');
  
  let timelineData = [];
  
  // 解析事件日志
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.match(/^### 📌 \d{2}:\d{2} 事件日志$/)) {
      const timeMatch = line.match(/### 📌 (\d{2}:\d{2}) 事件日志/);
      const endTime = timeMatch ? timeMatch[1] : '';
      
      let startTime = '';
      let duration = '';
      let activity = '';
      
      // 查找时间段和活动信息
      for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
        const nextLine = lines[j];
        
        if (nextLine.includes('**时间段：**')) {
          const rangeMatch = nextLine.match(/`([^`]+)`\s*(\d+)min/);
          if (rangeMatch) {
            const timeRange = rangeMatch[1];
            duration = rangeMatch[2];
            const timeParts = timeRange.split(' - ');
            if (timeParts.length === 2) {
              startTime = timeParts[0];
            }
          }
        } else if (nextLine.includes('**做了什么：**')) {
          for (let k = j + 1; k < lines.length; k++) {
            if (lines[k].includes('**当时想法与感悟：**')) break;
            if (lines[k].trim().startsWith('- ') || lines[k].trim().startsWith('\t- ')) {
              activity += lines[k].trim().replace(/^- /, '') + ' ';
            }
          }
          break;
        }
      }
      
      if (startTime && endTime && activity) {
        timelineData.push({
          startTime,
          endTime,
          duration: parseInt(duration) || 0,
          activity: activity.trim()
        });
      }
    }
  }
  
  // 按开始时间排序
  timelineData.sort((a, b) => a.startTime.localeCompare(b.startTime));
  
  if (timelineData.length === 0) {
    dv.paragraph("📝 今天还没有学习记录");
  } else {
    // 创建时间线可视化
    dv.header(3, `📅 ${today} 学习时间线`);
    
    // 计算总学习时间
    const totalMinutes = timelineData.reduce((sum, item) => sum + item.duration, 0);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    dv.paragraph(`**总学习时长：** ${hours}小时${minutes}分钟`);
    dv.paragraph(`**学习次数：** ${timelineData.length}次`);
    
    // 生成时间线HTML
    let timelineHtml = '<div style="position: relative; margin: 20px 0;">';
    
    for (let i = 0; i < timelineData.length; i++) {
      const item = timelineData[i];
      const isEven = i % 2 === 0;
      
      timelineHtml += `
        <div style="
          display: flex; 
          align-items: center; 
          margin: 15px 0;
          ${isEven ? 'flex-direction: row;' : 'flex-direction: row-reverse;'}
        ">
          <div style="
            flex: 1; 
            padding: 10px 15px; 
            background: ${isEven ? '#f0f8ff' : '#fff8f0'}; 
            border-radius: 8px; 
            border-left: 4px solid ${isEven ? '#4a90e2' : '#f39c12'};
            margin: ${isEven ? '0 20px 0 0' : '0 0 0 20px'};
          ">
            <div style="font-weight: bold; color: #333;">
              ${item.startTime} - ${item.endTime} (${item.duration}分钟)
            </div>
            <div style="margin-top: 5px; color: #666;">
              ${item.activity}
            </div>
          </div>
          <div style="
            width: 12px; 
            height: 12px; 
            background: ${isEven ? '#4a90e2' : '#f39c12'}; 
            border-radius: 50%; 
            border: 3px solid white;
            box-shadow: 0 0 0 2px ${isEven ? '#4a90e2' : '#f39c12'};
          "></div>
        </div>
      `;
    }
    
    timelineHtml += '</div>';
    
    // 显示时间线
    const timelineContainer = dv.container.createDiv();
    timelineContainer.innerHTML = timelineHtml;
  }
}
```

## 📊 最近一周学习时间线

```dataviewjs
// 获取最近7天的学习数据
const today = new Date();
let weekData = [];

for (let i = 0; i < 7; i++) {
  const date = new Date(today);
  date.setDate(date.getDate() - i);
  const dateStr = date.toISOString().slice(0, 10);
  
  const dayFile = dv.page(`🎓 study/📆 学习日志/日记/${dateStr}`);
  
  if (dayFile) {
    const content = await dv.io.load(dayFile.file.path);
    const lines = content.split('\n');
    
    let dayTotal = 0;
    let sessionCount = 0;
    
    for (let line of lines) {
      if (line.includes('**时间段：**')) {
        const minuteMatch = line.match(/(\d+)min/);
        if (minuteMatch) {
          dayTotal += parseInt(minuteMatch[1]);
          sessionCount++;
        }
      }
    }
    
    weekData.push({
      date: dateStr,
      dayOfWeek: date.toLocaleDateString('zh-CN', { weekday: 'short' }),
      totalMinutes: dayTotal,
      sessions: sessionCount
    });
  }
}

// 按日期排序
weekData.sort((a, b) => a.date.localeCompare(b.date));

if (weekData.length > 0) {
  dv.header(3, "📈 最近一周学习统计");
  
  // 创建柱状图样式的可视化
  const maxMinutes = Math.max(...weekData.map(d => d.totalMinutes));
  
  let chartHtml = '<div style="display: flex; align-items: end; height: 200px; margin: 20px 0; padding: 10px; background: #f9f9f9; border-radius: 8px;">';
  
  for (let data of weekData) {
    const height = maxMinutes > 0 ? (data.totalMinutes / maxMinutes) * 150 : 0;
    const hours = Math.floor(data.totalMinutes / 60);
    const minutes = data.totalMinutes % 60;
    
    chartHtml += `
      <div style="
        flex: 1; 
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        margin: 0 5px;
      ">
        <div style="
          width: 30px; 
          height: ${height}px; 
          background: linear-gradient(to top, #4a90e2, #87ceeb); 
          border-radius: 4px 4px 0 0;
          margin-bottom: 5px;
          position: relative;
        " title="${data.totalMinutes}分钟, ${data.sessions}次学习">
        </div>
        <div style="font-size: 12px; text-align: center;">
          <div style="font-weight: bold;">${data.dayOfWeek}</div>
          <div style="color: #666;">${data.date.slice(5)}</div>
          <div style="color: #4a90e2; font-size: 10px;">
            ${hours > 0 ? hours + 'h' : ''}${minutes}m
          </div>
        </div>
      </div>
    `;
  }
  
  chartHtml += '</div>';
  
  const chartContainer = dv.container.createDiv();
  chartContainer.innerHTML = chartHtml;
  
  // 显示详细数据表格
  dv.table(
    ["日期", "星期", "学习时长", "学习次数"],
    weekData.map(d => [
      d.date,
      d.dayOfWeek,
      `${Math.floor(d.totalMinutes / 60)}小时${d.totalMinutes % 60}分钟`,
      d.sessions
    ])
  );
}
```

## 🎨 自定义样式

你可以通过 CSS 片段来进一步美化时间线显示效果。创建一个 CSS 文件并添加以下样式：

```css
/* 时间线样式优化 */
.timeline-container {
  position: relative;
  padding: 20px 0;
}

.timeline-item {
  position: relative;
  margin: 20px 0;
  padding-left: 30px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: -20px;
  width: 2px;
  background: #ddd;
}

.timeline-item::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4a90e2;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #4a90e2;
}
```
